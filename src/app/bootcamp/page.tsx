"use client";

import React from "react";
import Navbar from "../components/Navbar/Navbar";
import { GradientWrapper } from "../components/gradient-wrapper";
import Image from "next/image";
import Link from "next/link";
import { AppButton } from "../components/app-button";
import { H1, Body3 } from "../components/app-typography";

const BOOTCAMP_VIDEOS = [
  {
    id: 1,
    title: "Bootcamp 1",
    url: "https://x.com/i/broadcasts/1yoKMogdmLlJQ",
    image: "/bootcamp/chimera-1.png",
  },
  {
    id: 2,
    title: "Bootcamp 2",
    url: "https://x.com/i/broadcasts/1mrGmPDlqgQKy",
    image: "/bootcamp/chimera-2.png",
  },
  {
    id: 3,
    title: "Bootcamp 3",
    url: "https://x.com/i/broadcasts/1dRKZYvXNgvxB",
    image: "/bootcamp/chimera-3.png",
  },
  {
    id: 4,
    title: "Bootcamp 4",
    url: "https://x.com/i/broadcasts/1BdxYqLoRgBxX",
    image: "/bootcamp/chimera-4.png",
  },
  {
    id: 5,
    title: "Bootcamp 5",
    url: "https://x.com/i/broadcasts/1vOxwXWEpnqKB",
    image: "/bootcamp/chimera-5.png",
  },
] as const;

const BOOTCAMP_FEATURES = [
  "Theory explanation",
  "Coded Walkthrough",
  "Coding Session",
] as const;

const DISCORD_LINK = "/discord";

interface BootcampVideoProps {
  video: (typeof BOOTCAMP_VIDEOS)[number];
}

function BootcampVideo({ video }: BootcampVideoProps) {
  return (
    <div className="mb-10">
      <Link href={video.url} target="_blank" rel="noopener noreferrer">
        <Image
          src={video.image}
          alt={video.title}
          width={800}
          height={418}
          className="rounded-lg border border-stroke-neutral-decorative transition-transform duration-200 hover:scale-105"
        />
      </Link>
    </div>
  );
}

function BootcampHeader() {
  return (
    <div className="mb-12 text-center">
      <H1 className="mb-6">Invariant Testing Bootcamp at Home</H1>

      <Body3 color="primary" className=" text-white">
        Learn invariant testing at your own pace, each video includes:{" "}
        {BOOTCAMP_FEATURES.join(", ")}
      </Body3>

      <div className="flex flex-col items-center gap-4">
        <Body3 color="primary" className="text-white">
          Join like-minded researchers, ask questions and go deeper in our
          Discord Community
        </Body3>
        <Link href={DISCORD_LINK}>
          <AppButton variant="primary" size="lg">
            Join Recon Discord
          </AppButton>
        </Link>
      </div>
    </div>
  );
}

function BootcampVideoGrid() {
  return (
    <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2">
      {BOOTCAMP_VIDEOS.map((video) => (
        <BootcampVideo key={video.id} video={video} />
      ))}
    </div>
  );
}

export default function Bootcamp() {
  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <GradientWrapper />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <BootcampHeader />
            <BootcampVideoGrid />
          </section>
        </main>
      </div>
    </div>
  );
}
