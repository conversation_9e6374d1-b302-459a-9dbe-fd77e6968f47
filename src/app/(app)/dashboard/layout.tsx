"use client";
import type { Viewport } from "next";
import type { ReactNode } from "react";

import { DashboardNavbar } from "./components/dashboard-navbar/dashboard-navbar";
import JobsDropDown from "../../components/jobs-dropdown/jobs-dropdown";

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#060d1c" },
  ],
};

/** END SIDEBAR LINKS */

export default function DashboardLayout({ children }: { children: ReactNode }) {
  // TODO: Context, Pro vs Demo
  // Pro get's this, Demo doesn't
  // Or just initially, deploy Pro on separate URL to save time

  return (
    <main className="flex min-h-[100vh] min-w-[600px] items-stretch">
      <DashboardNavbar />
      <div className="grow overflow-y-auto bg-dashboardBG">{children}</div>
      <JobsDropDown />
    </main>
  );
}
