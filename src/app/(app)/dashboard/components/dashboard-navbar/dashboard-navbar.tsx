import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { <PERSON>aB<PERSON>, <PERSON>aD<PERSON>rd, <PERSON>a<PERSON>oon, FaSun } from "react-icons/fa";

import { useGetDiscord } from "@/app/services/discord.hook.ts";
import { OrgStatus, useGetMyOrg } from "@/app/services/organization.hooks";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { AppLogo } from "../../../../components/app-logo";
import { AppSwitch } from "../../../../components/app-switch";
import { DashboardUser } from "./dashboard-user";
import { SidebarItem } from "./sidebar-item";
import { UpgradeToPro } from "./upgrade-to-pro";
import { useGetSidebarLinks } from "./use-get-sidebar-links";

export const DashboardNavbar = () => {
  // NOTE: This ensure we preload installs
  const { items, isChildItemsLoading } = useGetSidebarLinks();
  const { data: organization, isLoading, orgStatus } = useGetMyOrg();
  const { setTheme, theme: currentTheme } = useTheme();
  const [isDark, setIsDark] = useState(false);

  const { data: discordUrl } = useGetDiscord();
  const pathname = usePathname();

  const hideSidebar =
    pathname.includes("/shares/") || pathname.includes("/tools/medusa");

  const onThemeChange = (darkEnabled: boolean) => {
    setTheme(darkEnabled ? THEME_OPTIONS.dark : THEME_OPTIONS.light);
  };

  const ThemeIcon = isDark ? FaSun : FaMoon;

  useEffect(() => {
    // This will only run on the client-side
    setIsDark(currentTheme === THEME_OPTIONS.dark);
  }, [currentTheme]);

  if (hideSidebar) return null;

  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname
    <aside className="navbar-side aside-menu min-w-[300px] max-w-[300px] overflow-y-auto bg-aside pb-[27px]">
      <Link
        href="/dashboard"
        className="block cursor-pointer pl-[29px] pt-[33px]"
      >
        <AppLogo />
      </Link>
      <ul className="mt-[24px]">
        {items.map((item) => (
          <SidebarItem
            key={item.label}
            {...item}
            isChildItemsLoading={isChildItemsLoading}
          />
        ))}
      </ul>
      <a
        href={discordUrl}
        target="_blank"
        rel="nofollow noreferrer"
        className="cursor-pointer"
      >
        <div className="mb-[55px] ml-[28px] mt-[24px] flex gap-[18px] text-[16px] leading-[19px] text-textPrimary">
          <FaDiscord className="h-[16px] w-[20px]" />
          Discord
        </div>
      </a>
      <a
        href={"https://book.getrecon.xyz/"}
        target="_blank"
        rel="nofollow noreferrer"
        className="cursor-pointer"
      >
        <div className="mb-[55px] ml-[28px] mt-[24px] flex gap-[18px] text-[16px] leading-[19px] text-textPrimary">
          <FaBook className="h-[16px] w-[20px]" />
          Tutorials
        </div>
      </a>

      {orgStatus === OrgStatus.FREE && <UpgradeToPro />}

      <div className="px-[18px]">
        <div className="mb-[49px] mt-auto flex w-full items-center  justify-between">
          <ThemeIcon className="mr-[18px] text-[#CFCFCF]" />
          <span className="text-[16px] leading-[19px] text-textSecondary">
            Switch to {isDark ? "Light" : "Dark"} theme
          </span>
          <AppSwitch
            className="ml-auto"
            onChange={onThemeChange}
            enabled={isDark}
          />
        </div>

        <div className="gradient-dark-bg flex w-full items-center gap-[15px] self-center rounded-[8px] bg-blockBg p-[12px]">
          <DashboardUser
            {...{
              organization,
              isLoading,
              orgStatus,
            }}
          />
        </div>
      </div>
    </aside>
  );
};
