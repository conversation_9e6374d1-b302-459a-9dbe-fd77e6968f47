"use client";

import Link from "next/link";

import { AppButton } from "../components/app-button";
import { OrgStatus, useGetMyOrg } from "../services/organization.hooks";

export default function Dashboard() {
  const { isLoading, orgStatus } = useGetMyOrg();

  return (
    <div className="pl-[50px] pt-[50px] text-textPrimary">
      <h3 className="text-[35px]">Welcome to Recon V1.0!</h3>
      <p>
        Recon helps you write and run invariant tests with Echidna and Medusa
      </p>
      <p>Check the videos below or click around to learn more!</p>
      <p>Recon Builder is Free to use for Open Source Foundry Projects</p>
      <p>
        If you want to Run Invariant Tests in the Cloud, talk to us about Recon
        Pro!
      </p>
      <Link href="https://book.getrecon.xyz/" target="_blank">
        <AppButton>
          <p>Written Tutorials</p>
        </AppButton>
      </Link>

      {orgStatus === OrgStatus.NOORG && !isLoading && (
        <Link href="/dashboard/onboard">
          <h3 className="text-[35px]">You don't have an account</h3>
          <AppButton>Create an account!</AppButton>
        </Link>
      )}

      <div className="text-[35px]">
        <h2 className="mt-[25px]">Demo</h2>
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/z0sktBuJfEI"
          title=""
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        ></iframe>

        <h2 className="mt-[25px]">5 Minutes Tutorial</h2>
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/RwfiPrxbdBg"
          title=""
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        ></iframe>

        <h2 className="mt-[25px]">15 minutes product tour</h2>
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/TvCm6MCHKs0?si=PC5k2P7Rt_UfLK6Q"
          title=""
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        ></iframe>

        <h2 className="mt-[25px]">
          1 Hour workshop on writing better invariants
        </h2>
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/fXG2JwvoFZ0?si=P8Aa5qOIiwyh1avh"
          title=""
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        ></iframe>
      </div>
    </div>
  );
}
