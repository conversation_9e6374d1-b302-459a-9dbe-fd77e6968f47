import { ABIActionsRow } from "@/app/dashboard/handlers/[identifier]/abi-actions-row";
import { <PERSON><PERSON>rovider } from "@/app/dashboard/handlers/[identifier]/abi-context";
import { BeforeAfterContracts } from "@/app/dashboard/handlers/[identifier]/before-after-contracts";
import { ResultsPage } from "@/app/dashboard/handlers/[identifier]/results-page";
import { SelectContracts } from "@/app/dashboard/handlers/[identifier]/select-contracts";
import React, { useState } from "react";
import { AppButton } from "../app-button";
import { AppTabs, AppTabPane } from "../app-tabs";
import { Title2Strong, Body1 } from "../app-typography";
import { cn } from "@/lib/utils";
import styles from "./sandboxBuilder.module.scss";

// Constants for sandbox builder
const SANDBOX_CONFIG = {
  DEFAULT_CONTRACT_NAME: "Your_contract",
  DEFAULT_ABI_PLACEHOLDER: (index: number) => `Paste ABI ${index + 1} here`,
  CONTRACT_NAME_PLACEHOLDER: "Contract name",
} as const;

// Types
interface AbiInput {
  content: string;
  name: string;
}

interface ParsedAbi {
  name: string;
  abi: any;
  abiPath: string;
}

// Utility functions
const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const parseAbiInput = (input: AbiInput, index: number): ParsedAbi => {
  const obj = JSON.parse(input.content);
  return {
    name: input.name,
    abi: obj?.abi ? obj.abi : obj,
    abiPath: `src${index}`,
  };
};

// Reusable components
interface AbiInputFieldProps {
  input: AbiInput;
  index: number;
  onContentChange: (index: number, value: string) => void;
  onNameChange: (index: number, value: string) => void;
}

function AbiInputField({
  input,
  index,
  onContentChange,
  onNameChange,
}: AbiInputFieldProps) {
  return (
    <div className={cn(styles.inputContainer, "mb-4")}>
      <textarea
        value={input.content}
        onChange={(e) => onContentChange(index, e.target.value)}
        placeholder={SANDBOX_CONFIG.DEFAULT_ABI_PLACEHOLDER(index)}
        className="w-full rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary p-3 text-fore-neutral-primary focus:border-accent-primary focus:outline-none"
        rows={6}
      />
      <input
        type="text"
        value={input.name}
        onChange={(e) => onNameChange(index, e.target.value)}
        placeholder={SANDBOX_CONFIG.CONTRACT_NAME_PLACEHOLDER}
        className={cn(
          styles.contractNameInput,
          "mt-2 w-full rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary p-3 text-fore-neutral-primary focus:border-accent-primary focus:outline-none"
        )}
      />
    </div>
  );
}

interface ErrorDisplayProps {
  errors: string[];
}

function ErrorDisplay({ errors }: ErrorDisplayProps) {
  if (errors.length === 0) return null;

  return (
    <div
      className={cn(
        styles.errorContainer,
        "mb-4 rounded-lg bg-red-50 border border-red-200 p-4"
      )}
    >
      <Body1 color="primary" className="text-red-600">
        {errors.join(", ")}
      </Body1>
    </div>
  );
}

interface HandlersBuilderProps {
  preparedAbis: ParsedAbi[];
}

function HandlersBuilder({ preparedAbis }: HandlersBuilderProps) {
  return (
    <ABIProvider identifier={preparedAbis} forced={true}>
      <div className="p-11 lg:px-20">
        <Title2Strong color="primary" className="mb-5">
          Build your Handlers
        </Title2Strong>
        <div className="flex gap-10">
          <AppTabs defaultActiveKey="select-contacts" className="min-w-[400px]">
            <AppTabPane tab="select-contacts" label="Contracts">
              <SelectContracts />
            </AppTabPane>
            <AppTabPane
              tab="before-after-contacts"
              label="Before and after trackers"
            >
              <BeforeAfterContracts />
            </AppTabPane>
          </AppTabs>
          <div className="min-w-[500px] grow">
            <ABIActionsRow />
            <ResultsPage />
          </div>
        </div>
      </div>
    </ABIProvider>
  );
}

export default function SandboxBuilder() {
  const [abiInputs, setAbiInputs] = useState<AbiInput[]>([
    { content: "", name: SANDBOX_CONFIG.DEFAULT_CONTRACT_NAME },
  ]);
  const [preparedAbis, setPreparedAbis] = useState<ParsedAbi[] | null>(null);
  const [errors, setErrors] = useState<string[]>([]);

  const addNewAbi = () => {
    setAbiInputs([
      ...abiInputs,
      { content: "", name: SANDBOX_CONFIG.DEFAULT_CONTRACT_NAME },
    ]);
  };

  const handleAbiChange = (index: number, value: string) => {
    const newInputs = [...abiInputs];
    newInputs[index].content = value;
    setAbiInputs(newInputs);
  };

  const handleNameChange = (index: number, value: string) => {
    const newInputs = [...abiInputs];
    const capitalizedValue = capitalizeFirstLetter(value);
    newInputs[index].name = capitalizedValue;
    setAbiInputs(newInputs);

    try {
      const parsed = newInputs.map(parseAbiInput);
      setPreparedAbis(parsed);
      setErrors([]);
    } catch (e) {
      setErrors(["Invalid JSON in one or more ABIs"]);
    }
  };

  const parseAbis = () => {
    try {
      const parsed = abiInputs.map(parseAbiInput);
      console.log(parsed);
      setPreparedAbis(parsed);
      setErrors([]);
    } catch (e) {
      setErrors(["Invalid JSON in one or more ABIs"]);
    }
  };

  return (
    <div className="w-full">
      {/* ABI Input Section */}
      <div className="mb-6">
        {abiInputs.map((input, index) => (
          <AbiInputField
            key={index}
            input={input}
            index={index}
            onContentChange={handleAbiChange}
            onNameChange={handleNameChange}
          />
        ))}
      </div>

      <div className="mb-6 flex items-center justify-center gap-4">
        <AppButton onClick={addNewAbi} variant="secondary">
          Add Another ABI
        </AppButton>
        <AppButton onClick={parseAbis} variant="primary">
          Parse ABIs
        </AppButton>
      </div>

      <ErrorDisplay errors={errors} />

      {preparedAbis && <HandlersBuilder preparedAbis={preparedAbis} />}
    </div>
  );
}
