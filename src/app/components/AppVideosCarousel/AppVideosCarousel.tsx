"use client";

import React from "react";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { H6 } from "../app-typography";
import VideoPlayer from "../VideoPlayer/VideoPlayer";

// Video data - can be used for dashboard, pro page, or any other page
const DEFAULT_VIDEOS = [
  {
    title: "Recon Euler EVC Demo",
    url: "https://www.youtube.com/embed/z0sktBuJfEI",
    duration: "7min",
    views: "53K views",
  },
  {
    title: "5 minutes tutorial",
    url: "https://www.youtube.com/embed/RwfiPrxbdBg",
    duration: "5min",
    views: "42K views",
  },
  {
    title: "15 minutes product tour",
    url: "https://www.youtube.com/embed/TvCm6MCHKs0?si=PC5k2P7Rt_UfLK6Q",
    duration: "15min",
    views: "38K views",
  },
  {
    title: "1 Hour workshop on writing better invariants",
    url: "https://www.youtube.com/embed/fXG2JwvoFZ0?si=P8Aa5qOIiwyh1avh",
    duration: "60min",
    views: "25K views",
  },
];

// Pro videos for the pro page
const PRO_VIDEOS = [
  {
    title: "Run a job in the cloud",
    url: "https://www.youtube.com/embed/s4ci9zgIHiI",
    duration: "5min",
    views: "32K views",
  },
  {
    title: "Create and re-use recipes",
    url: "https://www.youtube.com/embed/bXT8Ye2EaGs",
    duration: "8min",
    views: "28K views",
  },
  {
    title: "Run jobs on PR and commit",
    url: "https://www.youtube.com/embed/Fnz4P5kxAD0",
    duration: "6min",
    views: "24K views",
  },
];

interface VideoCardProps {
  video: (typeof DEFAULT_VIDEOS)[0];
  isActive?: boolean;
  onClick: () => void;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  isActive = false,
  onClick,
}) => {
  return (
    <div
      className={`flex w-[300px] cursor-pointer flex-col rounded-xl border transition-all duration-200 ${
        isActive
          ? "bg-accent-primary/10 border-accent-primary"
          : "hover:border-accent-primary/50 border-fore-neutral-quaternary bg-back-neutral-secondary"
      }`}
      onClick={onClick}
    >
      {/* Video Thumbnail */}
      <div className="relative h-40 overflow-hidden rounded-t-xl bg-back-neutral-tertiary">
        {/* Gradient background from gradient-wrapper pattern */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `linear-gradient(0deg, #5C25D2, #5C25D2), url('/bg-recon.jpeg')`,
            backgroundBlendMode: "color, normal",
            backgroundSize: "cover",
            backgroundPosition: "center",
            opacity: 0.8,
          }}
        />

        {/* Play Button Overlay */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex size-14 items-center justify-center rounded-full bg-white/90 backdrop-blur-sm">
            <div className="flex size-10 items-center justify-center rounded-full bg-gradient-to-br from-accent-primary to-accent-secondary">
              <div className="ml-1 size-0 border-y-[6px] border-l-8 border-y-transparent border-l-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="flex flex-col gap-2 p-4">
        <div className="flex flex-col gap-1">
          <H6
            className={`line-clamp-2 ${
              isActive ? "text-accent-primary" : "text-fore-neutral-primary"
            }`}
          >
            {video.title}
          </H6>
          <p className="text-sm text-fore-neutral-tertiary">
            {video.duration} • {video.views}
          </p>
        </div>
      </div>
    </div>
  );
};

interface AppVideosCarouselProps {
  videos?: typeof DEFAULT_VIDEOS;
  title?: string;
  showTitle?: boolean;
}

export default function AppVideosCarousel({
  videos = DEFAULT_VIDEOS,
  title = "Recon PRO Workflows",
  showTitle = true,
}: AppVideosCarouselProps) {
  const [selectedVideo, setSelectedVideo] = React.useState(videos[0]);

  const handleVideoSelect = (video: (typeof videos)[0]) => {
    setSelectedVideo(video);
  };

  const handlePrev = () => {
    const currentIndex = videos.findIndex(
      (video) => video.url === selectedVideo.url
    );
    const newIndex = currentIndex === 0 ? videos.length - 1 : currentIndex - 1;
    setSelectedVideo(videos[newIndex]);
  };

  const handleNext = () => {
    const currentIndex = videos.findIndex(
      (video) => video.url === selectedVideo.url
    );
    const newIndex = currentIndex === videos.length - 1 ? 0 : currentIndex + 1;
    setSelectedVideo(videos[newIndex]);
  };

  return (
    <div className="w-full">
      {showTitle && (
        <h3 className="sub-title-custom mb-8 font-bold uppercase tracking-normal text-white lg:text-[40px]">
          {title}
        </h3>
      )}

      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <H6 className="text-fore-neutral-primary">Tutorial Videos</H6>

          <div className="flex gap-4">
            <button
              onClick={handlePrev}
              className="flex size-8 items-center justify-center rounded-full border border-accent-primary bg-back-neutral-secondary p-1.5 text-fore-neutral-primary transition-all duration-200 hover:bg-accent-primary hover:text-white"
              aria-label="Previous video"
            >
              <FiChevronLeft size={16} />
            </button>
            <button
              onClick={handleNext}
              className="flex size-8 items-center justify-center rounded-full border border-accent-primary bg-back-neutral-secondary p-1.5 text-fore-neutral-primary transition-all duration-200 hover:bg-accent-primary hover:text-white"
              aria-label="Next video"
            >
              <FiChevronRight size={16} />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-[2fr_1fr]">
        <div className="order-2 lg:order-1">
          <VideoPlayer
            link={selectedVideo.url}
            overlayText={`Watch ${selectedVideo.title}`}
          />
        </div>

        <div className="order-1 lg:order-2">
          <div className="flex max-h-[400px] gap-4 overflow-y-auto pb-2 lg:flex-col lg:overflow-visible lg:pb-0">
            {videos.map((video) => (
              <VideoCard
                key={video.url}
                video={video}
                isActive={selectedVideo.url === video.url}
                onClick={() => handleVideoSelect(video)}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Export PRO_VIDEOS for use in pro page
export { PRO_VIDEOS };
