import React from "react";
import Image from "next/image";
import { FaGithub } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { Title2Strong, Body2 } from "@/app/components/app-typography";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface TeamMemberProps {
  title: string;
  name: string;
  twitter: string;
  github: string;
  image: string;
  description: string;
}

// Constants for team member configuration
const TEAM_MEMBER_CONFIG = {
  IMAGE_HEIGHT: 160,
  IMAGE_WIDTH: 300,
  ICON_SIZE: 30,
  SOCIAL_ICON_SIZE: 50,
} as const;

// Reusable components
interface SocialLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  className?: string;
}

function SocialLink({ href, icon, label, className }: SocialLinkProps) {
  return (
    <Link
      href={href}
      className={cn(
        "transition-transform duration-200 hover:scale-110",
        className
      )}
      aria-label={label}
      target="_blank"
      rel="noopener noreferrer"
    >
      <div className="flex size-[50px] items-center justify-center rounded-full bg-accent-primary hover:bg-accent-secondary transition-colors duration-200">
        {icon}
      </div>
    </Link>
  );
}

interface TeamMemberImageProps {
  src: string;
  alt: string;
}

function TeamMemberImage({ src, alt }: TeamMemberImageProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden">
      <Image
        src={src}
        alt={alt}
        height={TEAM_MEMBER_CONFIG.IMAGE_HEIGHT}
        width={TEAM_MEMBER_CONFIG.IMAGE_WIDTH}
        className="w-full object-cover transition-transform duration-300 hover:scale-105"
      />
    </div>
  );
}

interface TeamMemberContentProps {
  name: string;
  description: string;
  twitter?: string;
  github?: string;
}

function TeamMemberContent({
  name,
  description,
  twitter,
  github,
}: TeamMemberContentProps) {
  return (
    <div className="flex grow flex-col justify-center p-4">
      <div className="grow space-y-2">
        <Title2Strong color="primary" className="text-white uppercase">
          {name}
        </Title2Strong>
        <Body2 color="primary" className="text-white">
          {description}
        </Body2>
      </div>

      <div className="mt-4 flex space-x-4">
        {twitter && (
          <SocialLink
            href={twitter}
            icon={
              <FaXTwitter
                className="text-white"
                size={TEAM_MEMBER_CONFIG.ICON_SIZE}
              />
            }
            label={`${name} on Twitter`}
          />
        )}
        {github && (
          <SocialLink
            href={github}
            icon={
              <FaGithub
                className="text-white"
                size={TEAM_MEMBER_CONFIG.ICON_SIZE}
              />
            }
            label={`${name} on GitHub`}
          />
        )}
      </div>
    </div>
  );
}

export default function TeamMember({
  image,
  name,
  twitter,
  github,
  description,
}: Omit<TeamMemberProps, "title">) {
  return (
    <div className="mr-7 flex w-[300px] flex-col overflow-hidden rounded-lg border border-accent-primary bg-back-accent-quaternary text-white shadow-lg transition-transform duration-300 hover:scale-105 md:w-[370px] lg:w-[370px]">
      <TeamMemberImage src={image} alt={name} />
      <TeamMemberContent
        name={name}
        description={description}
        twitter={twitter}
        github={github}
      />
    </div>
  );
}
